#!/usr/bin/env python3
"""
Comprehensive test for improved CV extraction
Tests the fixed fields: language, work experience & qualifications, and jobs done
"""

import os
import sys
import tempfile

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_cnc_cv_sample():
    """Create a sample CNC operator CV for testing"""
    return """
    <PERSON>ä<PERSON> und Dreher
    
    Persönliche Daten:
    Geboren: 12.08.1985
    Adresse: Industriestraße 45, 85356 Freising
    Nationalität: Deutsch
    Familienstand: Verheiratet
    Telefon: +49 8161 123456
    E-Mail: <EMAIL>
    
    Sprachkenntnisse:
    Deutsch: Muttersprache
    Englisch: Gute Kenntnisse
    Italienisch: Grundkenntnisse
    
    Berufserfahrung:
    2020-heute: CNC-Fräser bei Maschinenbau Schmidt GmbH
    - Programmierung und Bedienung von CNC-Fräsmaschinen (Fanuc, Siemens)
    - Werkzeugeinrichtung und Qualitätskontrolle
    - Wartung und Instandhaltung der Maschinen
    
    2018-2020: CNC-Dreher bei Präzisionsteile AG
    - Bedienung von CNC-Drehmaschinen
    - Erstellen von CNC-Programmen
    - Werkstückprüfung mit Messmitteln
    
    2015-2018: Maschinenbediener bei Metallverarbeitung Huber
    - Bedienung konventioneller Dreh- und Fräsmaschinen
    - Werkzeugwechsel und Einrichtung
    - Qualitätsprüfung der gefertigten Teile
    
    Qualifikationen:
    - 8 Jahre Berufserfahrung in der spanenden Fertigung
    - Expertise in CNC-Programmierung (G-Code, CAM-Software)
    - Kenntnisse in Werkstoffkunde und Fertigungstechnik
    - Qualitätssicherung und Messtechnik
    - Teamführung und Ausbildung von Lehrlingen
    
    Ausbildung:
    2012-2015: Ausbildung zum Zerspanungsmechaniker
    Berufsschule München, Abschluss: Sehr gut
    
    2011: Realschulabschluss
    Realschule Freising
    
    Weiterbildungen:
    2019: CNC-Programmierung Aufbaukurs
    2021: Qualitätsmanagement ISO 9001
    2022: Lean Manufacturing Grundlagen
    
    Fähigkeiten:
    - CNC-Fräsen und Drehen
    - CAM-Software (Mastercam, Fusion 360)
    - Messtechnik (Koordinatenmesstechnik, Lehren)
    - Werkzeugkunde und Schnittdatenoptimierung
    - Technisches Zeichnen und CAD-Grundlagen
    - Problemlösung und Prozessoptimierung
    
    Kündigungsfrist: 4 Wochen zum Monatsende
    Verfügbar ab: Nach Absprache
    """

def create_software_cv_sample():
    """Create a software developer CV for testing"""
    return """
    Maria Schmidt
    Senior Software Entwicklerin
    
    Persönliche Daten:
    Geboren: 15.03.1988
    Adresse: Maximilianstraße 12, 80539 München
    Nationalität: Deutsch
    Familienstand: Ledig
    Telefon: +49 89 987654321
    E-Mail: <EMAIL>
    
    Sprachkenntnisse:
    Deutsch: Muttersprache
    Englisch: Fließend
    Französisch: Grundkenntnisse
    Spanisch: Gute Kenntnisse
    
    Berufserfahrung:
    2021-heute: Senior Software Entwicklerin bei TechCorp GmbH
    - Full-Stack Entwicklung mit React und Node.js
    - Teamleitung und Mentoring von Junior Entwicklern
    - Architektur und Design von Microservices
    
    2019-2021: Software Entwicklerin bei StartupXYZ
    - Frontend Entwicklung mit Angular und TypeScript
    - Backend APIs mit Python und Django
    - DevOps und CI/CD Pipeline Setup
    
    2017-2019: Junior Software Entwicklerin bei WebSolutions AG
    - Webentwicklung mit HTML, CSS, JavaScript
    - Datenbankdesign und SQL Optimierung
    - Agile Entwicklung mit Scrum
    
    Qualifikationen:
    - 6 Jahre Berufserfahrung in der Softwareentwicklung
    - Expertise in modernen Web-Technologien
    - Erfahrung in Projektmanagement und Teamführung
    - Kenntnisse in Cloud-Technologien (AWS, Docker)
    - Agile Entwicklungsmethoden (Scrum, Kanban)
    
    Ausbildung:
    2014-2017: Bachelor of Science Informatik
    Technische Universität München, Abschluss: 1,8
    
    2013: Abitur
    Gymnasium München
    
    Weiterbildungen:
    2020: AWS Certified Solutions Architect
    2021: Scrum Master Zertifizierung
    2022: Machine Learning Grundlagen
    
    Fähigkeiten:
    - Programmiersprachen: Python, JavaScript, TypeScript, Java
    - Frameworks: React, Angular, Django, Spring Boot
    - Datenbanken: PostgreSQL, MongoDB, Redis
    - Cloud: AWS, Docker, Kubernetes
    - Tools: Git, Jenkins, JIRA, Confluence
    
    Kündigungsfrist: 3 Monate zum Monatsende
    Verfügbar ab: Sofort
    """

def test_improved_extraction():
    """Test the improved extraction with both CNC and Software CVs"""
    print("🔧 Testing Improved CV Extraction...")
    
    try:
        from enhanced_cv_extractor import EnhancedCVExtractor
        
        extractor = EnhancedCVExtractor()
        
        # Test cases
        test_cases = [
            ("CNC Operator", create_cnc_cv_sample(), "Hans Müller"),
            ("Software Developer", create_software_cv_sample(), "Maria Schmidt")
        ]
        
        for job_type, cv_text, candidate_name in test_cases:
            print(f"\n{'='*60}")
            print(f"🧪 Testing {job_type} CV")
            print(f"{'='*60}")
            
            # Create temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp_file:
                tmp_file.write(cv_text)
                tmp_file_path = tmp_file.name
            
            try:
                result = extractor.extract_comprehensive_cv_data(
                    tmp_file_path,
                    candidate_name=candidate_name,
                    cv_status="received"
                )
                
                # Test the three problematic fields
                test_fields = [
                    ('language', 'Language Skills'),
                    ('work_experience_qualifications', 'Work Experience & Qualifications'),
                    ('jobs_done', 'Jobs Done')
                ]
                
                for field_key, field_name in test_fields:
                    value = result.get(field_key, 'N/A')
                    print(f"\n🔍 {field_name}:")
                    print(f"   Result: {value}")
                    print(f"   Length: {len(str(value))} characters")
                    
                    # Validate results
                    if field_key == 'language':
                        if job_type == "CNC Operator":
                            expected = ['deutsch', 'englisch', 'italienisch']
                        else:
                            expected = ['deutsch', 'englisch', 'französisch', 'spanisch']
                        
                        found = [lang for lang in expected if lang.lower() in value.lower()]
                        success = len(found) >= 2
                        print(f"   ✅ Languages found: {found}" if success else f"   ❌ Expected more languages: {found}")
                        
                    elif field_key == 'work_experience_qualifications':
                        if job_type == "CNC Operator":
                            expected_terms = ['cnc', 'fräser', 'dreher', 'jahre', 'berufserfahrung']
                        else:
                            expected_terms = ['software', 'entwicklung', 'jahre', 'berufserfahrung']
                        
                        found = [term for term in expected_terms if term.lower() in value.lower()]
                        success = len(found) >= 3
                        print(f"   ✅ Key terms found: {found}" if success else f"   ❌ Expected more terms: {found}")
                        
                    elif field_key == 'jobs_done':
                        if job_type == "CNC Operator":
                            expected_jobs = ['cnc-fräser', 'cnc-dreher', 'maschinenbediener']
                        else:
                            expected_jobs = ['senior software entwicklerin', 'software entwicklerin', 'junior software entwicklerin']
                        
                        found = [job for job in expected_jobs if any(word in value.lower() for word in job.split())]
                        success = len(found) >= 1
                        print(f"   ✅ Jobs found: {found}" if success else f"   ❌ Expected jobs not found: {expected_jobs}")
                        print(f"   Raw jobs extracted: {value}")
                
            finally:
                try:
                    os.unlink(tmp_file_path)
                except (OSError, PermissionError):
                    pass
        
        return True
        
    except Exception as e:
        print(f"❌ Improved extraction test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """Test edge cases and different CV formats"""
    print(f"\n{'='*60}")
    print("🧪 Testing Edge Cases")
    print(f"{'='*60}")
    
    try:
        from enhanced_cv_extractor import EnhancedCVExtractor
        
        extractor = EnhancedCVExtractor()
        
        # Edge case: Minimal CV
        minimal_cv = """
        John Doe
        
        Languages: English (native), German (basic)
        
        Experience:
        2020-2023: Software Engineer at Tech Company
        
        Skills: Programming, Problem solving
        """
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp_file:
            tmp_file.write(minimal_cv)
            tmp_file_path = tmp_file.name
        
        try:
            result = extractor.extract_comprehensive_cv_data(tmp_file_path, candidate_name="John Doe")
            
            print(f"\n🔍 Minimal CV Test:")
            print(f"   Language: {result.get('language', 'N/A')}")
            print(f"   Work Experience: {result.get('work_experience_qualifications', 'N/A')}")
            print(f"   Jobs Done: {result.get('jobs_done', 'N/A')}")
            
            # Check if basic extraction works
            has_language = 'english' in result.get('language', '').lower()
            has_experience = 'software engineer' in result.get('work_experience_qualifications', '').lower()
            has_jobs = len(result.get('jobs_done', '')) > 0
            
            print(f"   ✅ Basic extraction working: Language={has_language}, Experience={has_experience}, Jobs={has_jobs}")
            
        finally:
            try:
                os.unlink(tmp_file_path)
            except (OSError, PermissionError):
                pass
        
        return True
        
    except Exception as e:
        print(f"❌ Edge case test failed: {e}")
        return False

def main():
    """Run comprehensive improved extraction tests"""
    print("🚀 Starting Comprehensive Improved CV Extraction Tests\n")
    
    tests = [
        test_improved_extraction,
        test_edge_cases
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All improved extraction tests passed!")
        print("\n✅ Improvements Summary:")
        print("   🌐 Language Field: Enhanced pattern matching with better level detection")
        print("   💼 Work Experience: Combined timeline extraction + qualifications sections")
        print("   🛠️ Jobs Done: Multiple extraction methods for comprehensive job title detection")
        print("\n🚀 Ready for production use!")
        return True
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
