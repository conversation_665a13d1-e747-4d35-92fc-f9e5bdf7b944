#!/usr/bin/env python3
"""
Test script to verify Excel integration is using the enhanced extractor
Simulates the exact same process as the app's extract-excel route
"""

import os
import sys
import tempfile

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_excel_integration():
    """Test that Excel integration uses enhanced extractor with all 18 fields"""
    print("📊 Testing Excel Integration with Enhanced Extractor...")
    
    try:
        # Import exactly as the app does
        from enhanced_cv_extractor import EnhancedCVExtractor
        enhanced_extractor = EnhancedCVExtractor()
        
        print("✅ Enhanced extractor imported and initialized successfully")
        
        # Create test CV
        test_cv = """
        <PERSON>-Fräser
        
        Persönliche Daten:
        Geboren: 25.06.1982
        Adresse: Werkstraße 15, 86899 Landsberg am Lech
        Nationalität: Deutsch
        Familienstand: Verheiratet
        Telefon: +49 8191 456789
        E-Mail: <EMAIL>
        
        Sprachkenntnisse:
        Deutsch: Muttersprache
        Englisch: Gute Kenntnisse
        
        Berufserfahrung:
        2019-heute: CNC-Fräser bei Präzisionsteile Müller GmbH
        - Programmierung und Bedienung von CNC-Fräsmaschinen
        - Werkzeugeinrichtung und Qualitätskontrolle
        
        Qualifikationen:
        - 10 Jahre Berufserfahrung in der spanenden Fertigung
        - Expertise in CNC-Programmierung
        - Kenntnisse in Werkstoffkunde
        
        Kündigungsfrist: 6 Wochen zum Quartalsende
        """
        
        # Create temporary file (simulating uploaded CV)
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp_file:
            tmp_file.write(test_cv)
            cv_path = tmp_file.name
        
        try:
            # Extract data exactly as the app does
            extracted_data = enhanced_extractor.extract_comprehensive_cv_data(
                cv_path,
                candidate_name="Klaus Weber",
                cv_status="received"
            )
            
            print("✅ Data extraction completed successfully")
            
            # Test all 18 fields that the app expects
            expected_fields = [
                'name', 'first_name', 'birthdate', 'street', 'postal_code', 
                'place_of_residence', 'nationality', 'marital_status', 'language',
                'work_experience_qualifications', 'jobs_done', 'past_companies',
                'last_job_names', 'notice_period', 'current_company', 
                'free_field_notes', 'upload_from_cv', 'status_of_application'
            ]
            
            print("\n📋 Testing All 18 Excel Fields:")
            
            missing_fields = []
            for field in expected_fields:
                value = extracted_data.get(field, 'MISSING')
                has_value = value and value != 'MISSING' and str(value).strip() != ''
                status = "✅" if has_value else "⚠️"
                print(f"   {status} {field}: {value}")
                
                if not has_value:
                    missing_fields.append(field)
            
            # Test the three key fields we fixed
            print("\n🔍 Testing Key Fixed Fields:")
            
            # Language field
            language_value = extracted_data.get('language', '')
            has_valid_languages = any(lang in language_value.lower() for lang in ['deutsch', 'englisch'])
            has_invalid_terms = any(term in language_value.lower() for term in ['company', 'developer', 'street'])
            print(f"   🌐 Language: '{language_value}'")
            print(f"      ✅ Has valid languages: {has_valid_languages}")
            print(f"      ✅ No invalid terms: {not has_invalid_terms}")
            
            # Work experience field
            experience_value = extracted_data.get('work_experience_qualifications', '')
            has_qualifications = 'qualifikationen' in experience_value.lower()
            has_years = 'jahre berufserfahrung' in experience_value.lower()
            print(f"   💼 Work Experience: '{experience_value[:100]}...'")
            print(f"      ✅ Has qualifications: {has_qualifications}")
            print(f"      ✅ Has years summary: {has_years}")
            
            # Jobs done field
            jobs_value = extracted_data.get('jobs_done', '')
            has_cnc_jobs = any(job in jobs_value.lower() for job in ['cnc', 'fräser'])
            print(f"   🛠️ Jobs Done: '{jobs_value}'")
            print(f"      ✅ Has CNC jobs: {has_cnc_jobs}")
            
            # Address fields
            street_value = extracted_data.get('street', '')
            place_value = extracted_data.get('place_of_residence', '')
            print(f"   🏠 Street: '{street_value}'")
            print(f"   🏠 Place: '{place_value}'")
            
            # Summary
            success_rate = (len(expected_fields) - len(missing_fields)) / len(expected_fields) * 100
            print(f"\n📊 Field Success Rate: {len(expected_fields) - len(missing_fields)}/{len(expected_fields)} ({success_rate:.1f}%)")
            
            if missing_fields:
                print(f"⚠️ Missing/Empty Fields: {missing_fields}")
            
            return True
            
        finally:
            try:
                os.unlink(cv_path)
            except (OSError, PermissionError):
                pass
        
    except Exception as e:
        print(f"❌ Excel integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_excel_headers():
    """Test that Excel headers match the enhanced extractor fields"""
    print("\n📋 Testing Excel Headers Compatibility...")
    
    # Headers from app_german.py
    app_headers = [
        'Name',
        'First Name', 
        'Birthdate',
        'Street',
        'Postal Code',
        'Place of Residence',
        'Nationality',
        'Marital Status',
        'Language',
        'Work Experience & Qualifications',
        'Jobs Done',
        'Past Companies',
        'Last Job Names',
        'Notice Period',
        'Current Company',
        'Free Field for Notes',
        'Upload from CV',
        'Status of Application'
    ]
    
    # Field keys from enhanced extractor
    extractor_fields = [
        'name', 'first_name', 'birthdate', 'street', 'postal_code',
        'place_of_residence', 'nationality', 'marital_status', 'language',
        'work_experience_qualifications', 'jobs_done', 'past_companies',
        'last_job_names', 'notice_period', 'current_company',
        'free_field_notes', 'upload_from_cv', 'status_of_application'
    ]
    
    print(f"✅ App headers count: {len(app_headers)}")
    print(f"✅ Extractor fields count: {len(extractor_fields)}")
    print(f"✅ Headers match: {len(app_headers) == len(extractor_fields)}")
    
    if len(app_headers) == len(extractor_fields):
        print("✅ All 18 fields properly mapped between app and extractor")
        return True
    else:
        print("❌ Mismatch between app headers and extractor fields")
        return False

def main():
    """Run Excel integration tests"""
    print("🚀 Starting Excel Integration Tests\n")
    
    tests = [
        test_excel_integration,
        test_excel_headers
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Excel integration is working correctly!")
        print("\n✅ Integration Status:")
        print("   📊 Enhanced extractor properly integrated with app")
        print("   📋 All 18 fields mapped correctly")
        print("   🔧 Fixed fields (language, experience, address) working")
        print("   🚀 Ready for Excel export in production")
        
        print("\n💡 If you're still seeing old results:")
        print("   1. Restart the Flask app to reload the enhanced_cv_extractor module")
        print("   2. Clear browser cache")
        print("   3. Try a hard refresh (Ctrl+F5)")
        
        return True
    else:
        print("⚠️ Some integration tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
