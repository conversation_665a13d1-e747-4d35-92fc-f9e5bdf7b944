#!/usr/bin/env python3
"""
Test script for CNC job CV extraction
Tests the problematic fields: language, work experience & qualifications, and jobs done
"""

import os
import sys
import tempfile

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_cnc_cv_sample():
    """Create a sample CNC operator CV for testing"""
    return """
    <PERSON> und Dreher
    
    Persönliche Daten:
    Geboren: 12.08.1985
    Adresse: Industriestraße 45, 85356 Freising
    Nationalität: Deutsch
    Familienstand: Verheiratet
    Telefon: +49 8161 123456
    E-Mail: <EMAIL>
    
    Sprachkenntnisse:
    Deutsch: Muttersprache
    Englisch: Gute Kenntnisse
    Italienisch: Grundkenntnisse
    
    Berufserfahrung:
    2020-heute: CNC-Fräser bei Maschinenbau Schmidt GmbH
    - Programmierung und Bedienung von CNC-Fräsmaschinen (Fanuc, Siemens)
    - Werkzeugeinrichtung und Qualitätskontrolle
    - Wartung und Instandhaltung der Maschinen
    
    2018-2020: CNC-Dreher bei Präzisionsteile AG
    - Bedienung von CNC-Drehmaschinen
    - Erstellen von CNC-Programmen
    - Werkstückprüfung mit Messmitteln
    
    2015-2018: Maschinenbediener bei Metallverarbeitung Huber
    - Bedienung konventioneller Dreh- und Fräsmaschinen
    - Werkzeugwechsel und Einrichtung
    - Qualitätsprüfung der gefertigten Teile
    
    Qualifikationen:
    - 8 Jahre Berufserfahrung in der spanenden Fertigung
    - Expertise in CNC-Programmierung (G-Code, CAM-Software)
    - Kenntnisse in Werkstoffkunde und Fertigungstechnik
    - Qualitätssicherung und Messtechnik
    - Teamführung und Ausbildung von Lehrlingen
    
    Ausbildung:
    2012-2015: Ausbildung zum Zerspanungsmechaniker
    Berufsschule München, Abschluss: Sehr gut
    
    2011: Realschulabschluss
    Realschule Freising
    
    Weiterbildungen:
    2019: CNC-Programmierung Aufbaukurs
    2021: Qualitätsmanagement ISO 9001
    2022: Lean Manufacturing Grundlagen
    
    Fähigkeiten:
    - CNC-Fräsen und Drehen
    - CAM-Software (Mastercam, Fusion 360)
    - Messtechnik (Koordinatenmesstechnik, Lehren)
    - Werkzeugkunde und Schnittdatenoptimierung
    - Technisches Zeichnen und CAD-Grundlagen
    - Problemlösung und Prozessoptimierung
    
    Kündigungsfrist: 4 Wochen zum Monatsende
    Verfügbar ab: Nach Absprache
    """

def test_current_enhanced_extractor():
    """Test the current enhanced extractor with CNC CV"""
    print("🔧 Testing Current Enhanced Extractor with CNC CV...")
    
    try:
        from enhanced_cv_extractor import EnhancedCVExtractor
        
        extractor = EnhancedCVExtractor()
        cnc_cv_text = create_cnc_cv_sample()
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp_file:
            tmp_file.write(cnc_cv_text)
            tmp_file_path = tmp_file.name
        
        try:
            result = extractor.extract_comprehensive_cv_data(
                tmp_file_path,
                candidate_name="Hans Müller",
                cv_status="received"
            )
            
            print("📋 Current Extraction Results:")
            
            # Focus on the problematic fields
            problematic_fields = [
                ('language', 'Language'),
                ('work_experience_qualifications', 'Work Experience & Qualifications'),
                ('jobs_done', 'Jobs Done')
            ]
            
            for field_key, field_name in problematic_fields:
                value = result.get(field_key, 'N/A')
                print(f"\n🔍 {field_name}:")
                print(f"   Result: {value}")
                print(f"   Length: {len(str(value))} characters")
                
                # Analyze the quality
                if field_key == 'language':
                    expected_langs = ['deutsch', 'englisch', 'italienisch']
                    found_langs = [lang for lang in expected_langs if lang.lower() in value.lower()]
                    print(f"   Expected languages found: {found_langs}")
                    
                elif field_key == 'work_experience_qualifications':
                    expected_terms = ['cnc', 'fräser', 'dreher', 'programmierung', 'qualifikationen']
                    found_terms = [term for term in expected_terms if term.lower() in value.lower()]
                    print(f"   Expected terms found: {found_terms}")
                    
                elif field_key == 'jobs_done':
                    expected_jobs = ['cnc-fräser', 'cnc-dreher', 'maschinenbediener']
                    found_jobs = [job for job in expected_jobs if job.lower() in value.lower()]
                    print(f"   Expected jobs found: {found_jobs}")
            
            return result
            
        finally:
            try:
                os.unlink(tmp_file_path)
            except (OSError, PermissionError):
                pass
        
    except Exception as e:
        print(f"❌ Current extractor test failed: {e}")
        return None

def test_bilingual_extractor_methods():
    """Test the existing bilingual extractor methods that work well"""
    print("\n🔍 Testing Existing Bilingual Extractor Methods...")
    
    try:
        from bilingual_cv_extractor import BilingualCVExtractorPatched
        
        extractor = BilingualCVExtractorPatched()
        cnc_cv_text = create_cnc_cv_sample()
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp_file:
            tmp_file.write(cnc_cv_text)
            tmp_file_path = tmp_file.name
        
        try:
            # Test individual methods
            result = extractor.extract_cv_data(tmp_file_path, ['experience', 'skills', 'education'])
            
            print("📋 Bilingual Extractor Results:")
            print(f"\n🔧 Experience: {result.get('experience', 'N/A')}")
            print(f"\n🛠️ Skills: {result.get('skills', 'N/A')}")
            print(f"\n🎓 Education: {result.get('education', 'N/A')}")
            
            return result
            
        finally:
            try:
                os.unlink(tmp_file_path)
            except (OSError, PermissionError):
                pass
        
    except Exception as e:
        print(f"❌ Bilingual extractor test failed: {e}")
        return None

def analyze_extraction_issues():
    """Analyze what's going wrong with the current extraction"""
    print("\n🔍 Analyzing Extraction Issues...")
    
    current_result = test_current_enhanced_extractor()
    bilingual_result = test_bilingual_extractor_methods()
    
    if current_result and bilingual_result:
        print("\n📊 Comparison Analysis:")
        
        # Compare language extraction
        current_lang = current_result.get('language', '')
        print(f"\n🌐 Language Field:")
        print(f"   Enhanced Extractor: '{current_lang}'")
        print(f"   Issue: Seems to be extracting too much or wrong content")
        
        # Compare experience extraction
        bilingual_exp = bilingual_result.get('experience', '')
        current_exp = current_result.get('work_experience_qualifications', '')
        print(f"\n💼 Experience Field:")
        print(f"   Bilingual Extractor: '{bilingual_exp}'")
        print(f"   Enhanced Extractor: '{current_exp[:100]}...' (truncated)")
        print(f"   Issue: Enhanced extractor may be too broad in section detection")
        
        # Compare skills (for jobs done reference)
        bilingual_skills = bilingual_result.get('skills', '')
        current_jobs = current_result.get('jobs_done', '')
        print(f"\n🛠️ Skills vs Jobs Done:")
        print(f"   Bilingual Skills: '{bilingual_skills}'")
        print(f"   Enhanced Jobs Done: '{current_jobs}'")
        print(f"   Issue: Jobs done extraction pattern may be too restrictive")
        
        return True
    
    return False

def main():
    """Run CNC extraction analysis"""
    print("🚀 Starting CNC CV Extraction Analysis\n")
    
    success = analyze_extraction_issues()
    
    if success:
        print("\n🎯 Recommendations for Improvement:")
        print("1. 🌐 Language Field: Use more targeted language section detection")
        print("2. 💼 Work Experience: Combine bilingual experience + qualifications sections")
        print("3. 🛠️ Jobs Done: Extract job titles from experience timeline, not just patterns")
        print("\n✅ Analysis complete! Ready to implement improvements.")
    else:
        print("\n❌ Analysis failed. Check extractor implementations.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
