{% extends 'base.html' %}

{% block title %}{{ job.title }} - Job Details{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-briefcase me-2"></i>{{ job.title }}
    </h1>
    <div class="btn-group" role="group">
        {% if job.cv_count > 0 %}
        <a href="{{ url_for('match') }}?job={{ job.title }}" class="btn btn-success">
            <i class="fas fa-check-circle me-1"></i> Match CVs
        </a>
        <a href="{{ url_for('job_email', job_title=job.title) }}" class="btn btn-warning">
            <i class="fas fa-envelope me-1"></i> Email Applicants
        </a>
        <button class="btn btn-info" onclick="showExtractModal('{{ job.title }}', {{ job.cv_count }})">
            <i class="fas fa-file-excel me-1"></i> Extract to Excel
        </button>
        {% endif %}
        <a href="{{ url_for('upload_cv', job=job.title) }}" class="btn btn-primary">
            <i class="fas fa-upload me-1"></i> Upload CV
        </a>
        <a href="{{ url_for('jobs') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Jobs
        </a>
    </div>
</div>

<!-- Job Status and Platform -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Job Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-tag me-2"></i>Status</h6>
                        <div class="d-flex align-items-center">
                            {% if job.status == 'Active' %}
                                <span class="badge bg-success fs-6 me-2">
                                    <i class="fas fa-circle me-1"></i>{{ job.status }}
                                </span>
                            {% elif job.status == 'Expired' %}
                                <span class="badge bg-danger fs-6 me-2">
                                    <i class="fas fa-circle me-1"></i>{{ job.status }}
                                </span>
                            {% elif job.status == 'Ending Soon' %}
                                <span class="badge bg-warning fs-6 me-2">
                                    <i class="fas fa-circle me-1"></i>{{ job.status }}
                                </span>
                            {% elif job.status == 'Paused' %}
                                <span class="badge bg-warning fs-6 me-2">
                                    <i class="fas fa-pause me-1"></i>{{ job.status }}
                                </span>
                            {% elif job.status == 'Closed' %}
                                <span class="badge bg-danger fs-6 me-2">
                                    <i class="fas fa-lock me-1"></i>{{ job.status }}
                                </span>
                            {% elif job.status == 'Draft' %}
                                <span class="badge bg-secondary fs-6 me-2">
                                    <i class="fas fa-edit me-1"></i>{{ job.status }}
                                </span>
                            {% else %}
                                <span class="badge bg-secondary fs-6 me-2">
                                    <i class="fas fa-circle me-1"></i>{{ job.status }}
                                </span>
                            {% endif %}
                            <button class="btn btn-outline-primary btn-sm" onclick="showStatusModal({{ job.id }}, '{{ job.status }}')">
                                <i class="fas fa-edit me-1"></i>Change
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-globe me-2"></i>Platform</h6>
                        {% if job.platform and job.job_url %}
                            <a href="{{ job.job_url }}" target="_blank" class="text-decoration-none">
                                <span class="badge bg-secondary fs-6">
                                    {{ job.platform }} <i class="fas fa-external-link-alt ms-1"></i>
                                </span>
                            </a>
                        {% elif job.platform %}
                            <span class="badge bg-secondary fs-6">{{ job.platform }}</span>
                        {% else %}
                            <span class="text-muted">Not specified</span>
                        {% endif %}
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-calendar-alt me-2"></i>Start Date</h6>
                        <p class="mb-0">{{ job.start_date or 'Not specified' }}</p>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-calendar-times me-2"></i>End Date</h6>
                        <p class="mb-0">{{ job.end_date or 'Not specified' }}</p>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-clock me-2"></i>Days Remaining</h6>
                        {% if job.days_remaining is not none %}
                            {% if job.days_remaining > 0 %}
                                <span class="text-success fw-bold">{{ job.days_remaining }} days</span>
                            {% elif job.days_remaining == 0 %}
                                <span class="text-warning fw-bold">Expires today</span>
                            {% else %}
                                <span class="text-danger fw-bold">Expired {{ job.days_remaining|abs }} days ago</span>
                            {% endif %}
                        {% else %}
                            <span class="text-muted">No end date</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Applications
                </h5>
            </div>
            <div class="card-body text-center">
                <h2 class="display-4 text-success mb-0">{{ job.cv_count }}</h2>
                <p class="text-muted mb-3">Total Applications</p>
                {% if job.cv_count > 0 %}
                    <a href="{{ url_for('cvs', job=job.title) }}" class="btn btn-outline-success">
                        <i class="fas fa-eye me-1"></i> View All CVs
                    </a>
                {% else %}
                    <p class="text-muted">No applications yet</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Applicant Status Summary -->
{% if job.cv_count > 0 and job.status_counts %}
<div class="card mb-4">
    <div class="card-header bg-warning text-dark">
        <h5 class="card-title mb-0">
            <i class="fas fa-users me-2"></i>Applicant Status Summary
        </h5>
    </div>
    <div class="card-body">
        <div class="row text-center">
            <div class="col">
                <a href="{{ url_for('cvs', job=job.title, status='received') }}" class="text-decoration-none">
                    <div class="border rounded p-3 h-100 status-card" data-status="received">
                        <i class="fas fa-inbox text-info fa-2x mb-2"></i>
                        <h4 class="text-info">{{ job.status_counts.received }}</h4>
                        <small class="text-muted">Received</small>
                    </div>
                </a>
            </div>
            <div class="col">
                <a href="{{ url_for('cvs', job=job.title, status='under_review') }}" class="text-decoration-none">
                    <div class="border rounded p-3 h-100 status-card" data-status="under_review">
                        <i class="fas fa-search text-warning fa-2x mb-2"></i>
                        <h4 class="text-warning">{{ job.status_counts.under_review }}</h4>
                        <small class="text-muted">Under Review</small>
                    </div>
                </a>
            </div>
            <div class="col">
                <a href="{{ url_for('cvs', job=job.title, status='interview_scheduled') }}" class="text-decoration-none">
                    <div class="border rounded p-3 h-100 status-card" data-status="interview_scheduled">
                        <i class="fas fa-calendar text-primary fa-2x mb-2"></i>
                        <h4 class="text-primary">{{ job.status_counts.interview_scheduled }}</h4>
                        <small class="text-muted">Interview Scheduled</small>
                    </div>
                </a>
            </div>
            <div class="col">
                <a href="{{ url_for('cvs', job=job.title, status='rejected') }}" class="text-decoration-none">
                    <div class="border rounded p-3 h-100 status-card" data-status="rejected">
                        <i class="fas fa-times text-danger fa-2x mb-2"></i>
                        <h4 class="text-danger">{{ job.status_counts.rejected }}</h4>
                        <small class="text-muted">Rejected</small>
                    </div>
                </a>
            </div>
            <div class="col">
                <a href="{{ url_for('cvs', job=job.title, status='hired') }}" class="text-decoration-none">
                    <div class="border rounded p-3 h-100 status-card" data-status="hired">
                        <i class="fas fa-check text-success fa-2x mb-2"></i>
                        <h4 class="text-success">{{ job.status_counts.hired }}</h4>
                        <small class="text-muted">Hired</small>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Job Description -->
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-file-text me-2"></i>Job Description
        </h5>
    </div>
    <div class="card-body">
        <div class="job-description">
            {{ job.description|nl2br|safe }}
        </div>
    </div>
</div>

<!-- Responsible People -->
<div class="card mb-4">
    <div class="card-header bg-warning text-dark">
        <h5 class="card-title mb-0">
            <i class="fas fa-users me-2"></i>Responsible People
        </h5>
    </div>
    <div class="card-body">
        {% if job.responsible_people %}
            <div class="row">
                {% for person in job.responsible_people %}
                <div class="col-md-6 mb-3">
                    <div class="card {% if person.is_main_responsible %}border-primary{% else %}border-secondary{% endif %}">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="card-title mb-1">
                                        <i class="fas fa-user me-2"></i>{{ person.name }}
                                        {% if person.is_main_responsible %}
                                            <span class="badge bg-primary ms-2">Main</span>
                                        {% endif %}
                                    </h6>
                                    <p class="card-text mb-0">
                                        <i class="fas fa-envelope me-2"></i>
                                        <a href="mailto:{{ person.email }}">{{ person.email }}</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="text-muted">No responsible people assigned</p>
        {% endif %}
    </div>
</div>

<!-- Quick Actions -->
{% if job.cv_count > 0 %}
<div class="card">
    <div class="card-header bg-dark text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-bolt me-2"></i>Quick Actions
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <a href="{{ url_for('match') }}?job={{ job.title }}" class="btn btn-success w-100 mb-2">
                    <i class="fas fa-check-circle me-2"></i>
                    <div>Match CVs</div>
                    <small>Find best candidates</small>
                </a>
            </div>
            <div class="col-md-3">
                <a href="{{ url_for('job_email', job_title=job.title) }}" class="btn btn-warning w-100 mb-2">
                    <i class="fas fa-envelope me-2"></i>
                    <div>Email Applicants</div>
                    <small>Send mass emails</small>
                </a>
            </div>
            <div class="col-md-3">
                <button class="btn btn-info w-100 mb-2" onclick="showExtractModal('{{ job.title }}', {{ job.cv_count }})">
                    <i class="fas fa-file-excel me-2"></i>
                    <div>Extract to Excel</div>
                    <small>Download candidate data</small>
                </button>
            </div>
            <div class="col-md-3">
                <a href="{{ url_for('cvs', job=job.title) }}" class="btn btn-outline-primary w-100 mb-2">
                    <i class="fas fa-file-alt me-2"></i>
                    <div>View CVs</div>
                    <small>Browse all applications</small>
                </a>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3">
                <a href="{{ url_for('upload_cv', job=job.title) }}" class="btn btn-outline-secondary w-100 mb-2">
                    <i class="fas fa-upload me-2"></i>
                    <div>Upload CV</div>
                    <small>Add new application</small>
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Extract to Excel Modal (same as jobs.html) -->
<div class="modal fade" id="extractModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-excel me-2"></i>Extract CVs to Excel
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Extract candidate data from CVs for job: <strong id="extractJobTitle"></strong></p>
                <p class="text-muted">Total CVs available: <span id="extractTotalCVs"></span></p>

                <div class="mb-3">
                    <label for="extractCount" class="form-label">Number of candidates to extract:</label>
                    <select class="form-select" id="extractCount">
                        <option value="5">Top 5 candidates</option>
                        <option value="10">Top 10 candidates</option>
                        <option value="15">Top 15 candidates</option>
                        <option value="all">All candidates</option>
                    </select>
                </div>

                <div class="mb-3">
                    <h6 class="mb-3">Comprehensive CV Data Extraction:</h6>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Enhanced Extraction:</strong> All comprehensive CV fields will be extracted automatically including personal information, work history, qualifications, and more.
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Personal Information:</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success me-1"></i> Name & First Name</li>
                                <li><i class="fas fa-check text-success me-1"></i> Birthdate</li>
                                <li><i class="fas fa-check text-success me-1"></i> Address (Street, Postal Code, City)</li>
                                <li><i class="fas fa-check text-success me-1"></i> Nationality</li>
                                <li><i class="fas fa-check text-success me-1"></i> Marital Status</li>
                                <li><i class="fas fa-check text-success me-1"></i> Languages</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Professional Information:</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success me-1"></i> Work Experience & Qualifications</li>
                                <li><i class="fas fa-check text-success me-1"></i> Jobs Done</li>
                                <li><i class="fas fa-check text-success me-1"></i> Past Companies</li>
                                <li><i class="fas fa-check text-success me-1"></i> Last Job Names</li>
                                <li><i class="fas fa-check text-success me-1"></i> Notice Period</li>
                                <li><i class="fas fa-check text-success me-1"></i> Current Company</li>
                                <li><i class="fas fa-check text-success me-1"></i> Application Status</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="extractToExcel()">
                    <i class="fas fa-download me-1"></i>Download Excel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentJobTitle = '';

function showExtractModal(jobTitle, cvCount) {
    currentJobTitle = jobTitle;
    document.getElementById('extractJobTitle').textContent = jobTitle;
    document.getElementById('extractTotalCVs').textContent = cvCount;

    // Update the select options based on available CVs
    const selectElement = document.getElementById('extractCount');
    selectElement.innerHTML = '';

    if (cvCount >= 5) {
        selectElement.innerHTML += '<option value="5">Top 5 candidates</option>';
    }
    if (cvCount >= 10) {
        selectElement.innerHTML += '<option value="10">Top 10 candidates</option>';
    }
    if (cvCount >= 15) {
        selectElement.innerHTML += '<option value="15">Top 15 candidates</option>';
    }
    selectElement.innerHTML += '<option value="all">All ' + cvCount + ' candidates</option>';

    new bootstrap.Modal(document.getElementById('extractModal')).show();
}

function extractToExcel() {
    const count = document.getElementById('extractCount').value;

    // All comprehensive fields are automatically included
    const fields = ['comprehensive']; // Signal to use comprehensive extraction

    // Create download URL
    const params = new URLSearchParams({
        job: currentJobTitle,
        count: count,
        fields: fields.join(',')
    });

    // Trigger download
    window.location.href = '/extract-excel?' + params.toString();

    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('extractModal')).hide();
}

// Job Status Update Modal Functions
function showStatusModal(jobId, currentStatus) {
    document.getElementById('jobId').value = jobId;
    document.getElementById('currentStatus').textContent = currentStatus;
    document.getElementById('newStatus').value = currentStatus;
    new bootstrap.Modal(document.getElementById('statusModal')).show();
}

function updateJobStatus() {
    const jobId = document.getElementById('jobId').value;
    const newStatus = document.getElementById('newStatus').value;

    if (!newStatus) {
        alert('Please select a status.');
        return;
    }

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/update-job-status/${jobId}`;

    const statusInput = document.createElement('input');
    statusInput.type = 'hidden';
    statusInput.name = 'status';
    statusInput.value = newStatus;

    form.appendChild(statusInput);
    document.body.appendChild(form);
    form.submit();
}
</script>

<!-- Job Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Update Job Status
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Current Status: <strong id="currentStatus"></strong></p>
                <div class="mb-3">
                    <label for="newStatus" class="form-label">New Status:</label>
                    <select class="form-select" id="newStatus">
                        <option value="Active">Active</option>
                        <option value="Paused">Paused</option>
                        <option value="Closed">Closed</option>
                        <option value="Expired">Expired</option>
                        <option value="Draft">Draft</option>
                    </select>
                </div>
                <input type="hidden" id="jobId" value="">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateJobStatus()">
                    <i class="fas fa-save me-1"></i>Update Status
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}
