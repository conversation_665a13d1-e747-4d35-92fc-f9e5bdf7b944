<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BAUCH HR - Styling Test</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="static/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <img src="static/img/BAUCH_Group_Logo_4c_transparent.png" alt="BAUCH Group" class="navbar-brand-logo">
            </a>
            <div class="collapse navbar-collapse">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-briefcase me-1"></i>Jobs
                        </a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <!-- Language Switcher -->
                    <div class="dropdown me-2">
                        <button class="btn btn-outline-dark btn-sm dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-globe me-1"></i>English
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                            <li><a class="dropdown-item language-item" href="#">
                                <i class="fas fa-flag-usa me-2"></i>English
                            </a></li>
                            <li><a class="dropdown-item language-item" href="#">
                                <i class="fas fa-flag me-2"></i>Deutsch
                            </a></li>
                        </ul>
                    </div>
                    
                    <button class="btn btn-outline-dark btn-sm me-2" id="theme-toggle">
                        <i class="fas fa-moon" id="dark-icon"></i>
                        <i class="fas fa-sun d-none" id="light-icon"></i>
                    </button>
                    <a href="#" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Dashboard Header -->
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </h1>
            </div>
        </div>

        <!-- Dashboard Cards -->
        <div class="row">
            <!-- Jobs Card -->
            <div class="col-md-4 mb-4">
                <div class="card border-primary h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-briefcase me-2"></i>Jobs
                        </h5>
                    </div>
                    <div class="card-body">
                        <h2 class="display-4 text-center">5</h2>
                        <p class="text-center">Active job postings</p>
                    </div>
                    <div class="card-footer bg-transparent border-top-0">
                        <div class="d-grid gap-2">
                            <a href="#" class="btn btn-outline-primary">View Jobs</a>
                            <a href="#" class="btn btn-primary">Add New Job</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- CVs Card -->
            <div class="col-md-4 mb-4">
                <div class="card border-success h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-file-alt me-2"></i>CVs
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-center">Upload and manage candidate CVs for your job postings.</p>
                    </div>
                    <div class="card-footer bg-transparent border-top-0">
                        <div class="d-grid gap-2">
                            <a href="#" class="btn btn-outline-success">View CVs</a>
                            <a href="#" class="btn btn-success">Upload CV</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Matching Card -->
            <div class="col-md-4 mb-4">
                <div class="card border-info h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-check-circle me-2"></i>Matching
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-center">Use AI to match candidate CVs to your job requirements.</p>
                    </div>
                    <div class="card-footer bg-transparent border-top-0">
                        <div class="d-grid">
                            <a href="#" class="btn btn-info text-white">Match CVs</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Job List Sample -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-briefcase me-2"></i>Recent Jobs
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>Job Title</th>
                                <th>Status</th>
                                <th>CVs</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <a href="#" class="text-decoration-none">
                                        <strong class="text-primary">Software Developer</strong>
                                    </a>
                                    <br>
                                    <small class="text-muted">Full-stack development position</small>
                                </td>
                                <td>
                                    <span class="badge bg-success">
                                        <i class="fas fa-circle me-1"></i>Active
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-info">12 CVs</span>
                                </td>
                                <td>
                                    <a href="#" class="btn btn-sm btn-outline-primary">View</a>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <a href="#" class="text-decoration-none">
                                        <strong class="text-primary">CNC Operator</strong>
                                    </a>
                                    <br>
                                    <small class="text-muted">Manufacturing position</small>
                                </td>
                                <td>
                                    <span class="badge bg-warning">
                                        <i class="fas fa-pause me-1"></i>Paused
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-info">8 CVs</span>
                                </td>
                                <td>
                                    <a href="#" class="btn btn-sm btn-outline-primary">View</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="static/js/main.js"></script>
    
    <script>
        // Add some test text to verify styling
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Styling test page loaded');
            console.log('Language switcher should have black text in light mode');
            console.log('All text should be white in dark mode');
        });
    </script>
</body>
</html>
