"""
Enhanced CV Extractor for BAUCH HR Management System
Extracts comprehensive CV data including all requested fields:
- Personal information (name, first name, birthdate, address, nationality, marital status)
- Contact information (email, phone)
- Professional information (work history, qualifications, companies, job titles)
- Additional fields (language, notes, status)
"""

import re
import os
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple
import fitz  # PyMuPDF
from pathlib import Path

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

class EnhancedCVExtractor:
    """Enhanced CV extractor for comprehensive data extraction"""
    
    def __init__(self):
        self.german_months = {
            'januar': '01', 'februar': '02', 'märz': '03', 'april': '04',
            'mai': '05', 'juni': '06', 'juli': '07', 'august': '08',
            'september': '09', 'oktober': '10', 'november': '11', 'dezember': '12',
            'jan': '01', 'feb': '02', 'mär': '03', 'apr': '04',
            'jun': '06', 'jul': '07', 'aug': '08', 'sep': '09',
            'okt': '10', 'nov': '11', 'dez': '12'
        }
        
    def extract_comprehensive_cv_data(self, file_path: str, candidate_name: str = None, cv_status: str = "received") -> Dict[str, str]:
        """Extract all comprehensive CV data fields"""
        text = self._extract_text_from_file(file_path)
        if not text:
            # Return structure with empty values if text extraction fails
            filename = os.path.basename(file_path)
            return self._create_empty_result_structure(filename, candidate_name, cv_status)

        filename = os.path.basename(file_path)
        result = {}

        # Extract all fields
        result['name'] = self._extract_full_name(text, filename, candidate_name)
        result['first_name'] = self._extract_first_name(result['name'])
        result['birthdate'] = self._extract_birthdate(text)
        result['street'] = self._extract_street_address(text)
        result['postal_code'] = self._extract_postal_code(text)
        result['place_of_residence'] = self._extract_place_of_residence(text)
        result['nationality'] = self._extract_nationality(text)
        result['marital_status'] = self._extract_marital_status(text)
        result['language'] = self._extract_languages(text)
        result['work_experience_qualifications'] = self._extract_work_experience_and_qualifications(text)
        result['jobs_done'] = self._extract_jobs_done(text)
        result['past_companies'] = self._extract_past_companies(text)
        result['last_job_names'] = self._extract_last_job_names(text)
        result['notice_period'] = self._extract_notice_period(text)
        result['current_company'] = self._extract_current_company(text)
        result['free_field_notes'] = ""  # Empty field for HR notes
        result['upload_from_cv'] = filename
        result['status_of_application'] = cv_status

        return result

    def _create_empty_result_structure(self, filename: str, candidate_name: str = None, cv_status: str = "received") -> Dict[str, str]:
        """Create empty result structure with all required fields"""
        return {
            'name': candidate_name or "Unknown",
            'first_name': "",
            'birthdate': "",
            'street': "",
            'postal_code': "",
            'place_of_residence': "",
            'nationality': "",
            'marital_status': "",
            'language': "",
            'work_experience_qualifications': "",
            'jobs_done': "",
            'past_companies': "",
            'last_job_names': "",
            'notice_period': "",
            'current_company': "",
            'free_field_notes': "",
            'upload_from_cv': filename,
            'status_of_application': cv_status
        }
    
    def _extract_text_from_file(self, file_path: str) -> str:
        """Extract text from PDF, DOCX, or TXT file"""
        file_ext = Path(file_path).suffix.lower()

        if file_ext == '.pdf':
            try:
                with fitz.open(file_path) as doc:
                    return "\f".join(page.get_text("text") for page in doc)
            except Exception as e:
                print(f"Error reading PDF: {e}")
                return ""

        elif file_ext == '.docx' and DOCX_AVAILABLE:
            try:
                doc = Document(file_path)
                text_parts = []
                for paragraph in doc.paragraphs:
                    if paragraph.text.strip():
                        text_parts.append(paragraph.text.strip())
                return "\n".join(text_parts)
            except Exception as e:
                print(f"Error reading DOCX: {e}")
                return ""

        elif file_ext == '.txt':
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            except Exception as e:
                print(f"Error reading TXT: {e}")
                return ""

        return ""
    
    def _extract_full_name(self, text: str, filename: str, candidate_name: str = None) -> str:
        """Extract full name from CV"""
        if candidate_name:
            return candidate_name
        
        # Try to extract from first few lines
        lines = text.split('\n')[:10]
        for line in lines:
            line = line.strip()
            if self._looks_like_name(line):
                return line
        
        # Fallback to filename
        if filename:
            name_from_file = re.sub(r'[._-]', ' ', os.path.splitext(filename)[0])
            return name_from_file.title()
        
        return "Unknown"
    
    def _extract_first_name(self, full_name: str) -> str:
        """Extract first name from full name"""
        if not full_name or full_name == "Unknown":
            return ""
        
        parts = full_name.split()
        return parts[0] if parts else ""
    
    def _extract_birthdate(self, text: str) -> str:
        """Extract birthdate from CV"""
        # German date patterns
        date_patterns = [
            r'geboren\s*(?:am\s*)?(\d{1,2})[.\-/](\d{1,2})[.\-/](\d{4})',
            r'geburtsdatum\s*:?\s*(\d{1,2})[.\-/](\d{1,2})[.\-/](\d{4})',
            r'birth\s*date\s*:?\s*(\d{1,2})[.\-/](\d{1,2})[.\-/](\d{4})',
            r'born\s*:?\s*(\d{1,2})[.\-/](\d{1,2})[.\-/](\d{4})',
            r'(\d{1,2})[.\-/](\d{1,2})[.\-/](\d{4})\s*geboren',
        ]
        
        text_lower = text.lower()
        for pattern in date_patterns:
            match = re.search(pattern, text_lower)
            if match:
                day, month, year = match.groups()
                return f"{day.zfill(2)}.{month.zfill(2)}.{year}"
        
        return ""
    
    def _extract_street_address(self, text: str) -> str:
        """Extract street address from CV"""
        # German address patterns
        address_patterns = [
            r'([A-ZÄÖÜ][a-zäöüß]+(?:straße|str\.?|gasse|weg|platz|allee))\s*(\d+[a-z]?)',
            r'(\d+[a-z]?)\s+([A-ZÄÖÜ][a-zäöüß]+(?:straße|str\.?|gasse|weg|platz|allee))',
            r'adresse\s*:?\s*([^\n]+(?:straße|str\.?|gasse|weg|platz|allee)[^\n]*)',
        ]
        
        for pattern in address_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                if len(match.groups()) == 2:
                    return f"{match.group(1)} {match.group(2)}"
                else:
                    return match.group(1).strip()
        
        return ""
    
    def _extract_postal_code(self, text: str) -> str:
        """Extract postal code from CV"""
        # German postal code patterns (5 digits)
        postal_patterns = [
            r'\b(\d{5})\s+[A-ZÄÖÜ][a-zäöüß]+',  # 12345 München
            r'plz\s*:?\s*(\d{5})',
            r'postleitzahl\s*:?\s*(\d{5})',
        ]
        
        for pattern in postal_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return ""
    
    def _extract_place_of_residence(self, text: str) -> str:
        """Extract place of residence from CV"""
        # Look for city names after postal codes
        postal_city_pattern = r'\d{5}\s+([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)?)'
        match = re.search(postal_city_pattern, text)
        if match:
            return match.group(1)
        
        # Look for common German city indicators
        city_patterns = [
            r'wohnort\s*:?\s*([A-ZÄÖÜ][a-zäöüß]+)',
            r'ort\s*:?\s*([A-ZÄÖÜ][a-zäöüß]+)',
            r'stadt\s*:?\s*([A-ZÄÖÜ][a-zäöüß]+)',
        ]
        
        for pattern in city_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return ""
    
    def _extract_nationality(self, text: str) -> str:
        """Extract nationality from CV"""
        nationality_patterns = [
            r'nationalität\s*:?\s*([a-zäöüß]+)',
            r'staatsangehörigkeit\s*:?\s*([a-zäöüß]+)',
            r'nationality\s*:?\s*([a-z]+)',
            r'citizenship\s*:?\s*([a-z]+)',
        ]
        
        text_lower = text.lower()
        for pattern in nationality_patterns:
            match = re.search(pattern, text_lower)
            if match:
                return match.group(1).capitalize()
        
        return ""
    
    def _extract_marital_status(self, text: str) -> str:
        """Extract marital status from CV"""
        marital_patterns = [
            r'familienstand\s*:?\s*(ledig|verheiratet|geschieden|verwitwet)',
            r'marital\s*status\s*:?\s*(single|married|divorced|widowed)',
        ]
        
        text_lower = text.lower()
        for pattern in marital_patterns:
            match = re.search(pattern, text_lower)
            if match:
                return match.group(1).capitalize()
        
        return ""
    
    def _extract_languages(self, text: str) -> str:
        """Extract languages from CV"""
        # Look for language sections
        language_section = self._find_section(text, ['sprachen', 'languages', 'sprachkenntnisse'])
        if language_section:
            # Extract language names and levels
            languages = []
            lang_patterns = [
                r'(deutsch|englisch|französisch|spanisch|italienisch|russisch)\s*[:\-]?\s*(muttersprache|fließend|gut|grundkenntnisse|verhandlungssicher)',
                r'(german|english|french|spanish|italian|russian)\s*[:\-]?\s*(native|fluent|good|basic|business)',
            ]
            
            for pattern in lang_patterns:
                matches = re.findall(pattern, language_section.lower())
                for lang, level in matches:
                    languages.append(f"{lang.capitalize()}: {level.capitalize()}")
            
            return "; ".join(languages) if languages else language_section[:200]
        
        return ""
    
    def _extract_work_experience_and_qualifications(self, text: str) -> str:
        """Extract work experience and qualifications"""
        experience_section = self._find_section(text, ['berufserfahrung', 'work experience', 'professional experience', 'qualifikationen', 'qualifications'])
        if experience_section:
            return experience_section[:500]  # Limit to 500 characters
        
        return ""
    
    def _extract_jobs_done(self, text: str) -> str:
        """Extract list of jobs done"""
        jobs = []
        
        # Look for job title patterns
        job_patterns = [
            r'(\d{4})\s*[-–]\s*(?:(\d{4})|heute|present)\s*:?\s*([^\n]+?)(?:\s+bei\s+([^\n]+))?',
            r'([A-ZÄÖÜ][a-zäöüß\s]+(?:manager|entwickler|ingenieur|techniker|leiter|spezialist))',
        ]
        
        for pattern in job_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match) >= 3:
                    jobs.append(match[2].strip())
        
        return "; ".join(jobs[:5]) if jobs else ""  # Limit to 5 jobs
    
    def _extract_past_companies(self, text: str) -> str:
        """Extract past companies"""
        companies = []
        
        # Look for company patterns
        company_patterns = [
            r'bei\s+([A-ZÄÖÜ][a-zäöüß\s&]+(?:GmbH|AG|KG|e\.V\.|Inc\.|Ltd\.|Corp))',
            r'([A-ZÄÖÜ][a-zäöüß\s&]+(?:GmbH|AG|KG|e\.V\.|Inc\.|Ltd\.|Corp))',
        ]
        
        for pattern in company_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                company = match.strip()
                if len(company) > 3 and company not in companies:
                    companies.append(company)
        
        return "; ".join(companies[:5]) if companies else ""  # Limit to 5 companies
    
    def _extract_last_job_names(self, text: str) -> str:
        """Extract last job names/titles"""
        # Look for most recent job (usually at the top of experience section)
        lines = text.split('\n')
        for i, line in enumerate(lines):
            if re.search(r'berufserfahrung|work experience|professional experience', line.lower()):
                # Look in next few lines for job title
                for j in range(i+1, min(i+10, len(lines))):
                    job_line = lines[j].strip()
                    if job_line and len(job_line) > 5:
                        # Check if it looks like a job title
                        if re.search(r'(manager|entwickler|ingenieur|techniker|leiter|spezialist|analyst)', job_line.lower()):
                            return job_line
        
        return ""
    
    def _extract_notice_period(self, text: str) -> str:
        """Extract notice period information"""
        notice_patterns = [
            r'kündigungsfrist\s*:?\s*([^\n]+)',
            r'notice\s*period\s*:?\s*([^\n]+)',
            r'verfügbar\s*ab\s*:?\s*([^\n]+)',
            r'available\s*from\s*:?\s*([^\n]+)',
        ]
        
        text_lower = text.lower()
        for pattern in notice_patterns:
            match = re.search(pattern, text_lower)
            if match:
                return match.group(1).strip()
        
        return ""
    
    def _extract_current_company(self, text: str) -> str:
        """Extract current company"""
        # Look for "seit" or "current" indicators
        current_patterns = [
            r'seit\s+\d{4}\s*:?\s*[^\n]*bei\s+([A-ZÄÖÜ][a-zäöüß\s&]+(?:GmbH|AG|KG|e\.V\.|Inc\.|Ltd\.|Corp))',
            r'current\s*:?\s*[^\n]*at\s+([A-Z][a-zA-Z\s&]+(?:GmbH|AG|KG|e\.V\.|Inc\.|Ltd\.|Corp))',
        ]
        
        for pattern in current_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return ""
    
    def _find_section(self, text: str, section_keywords: List[str]) -> str:
        """Find a specific section in the CV text"""
        lines = text.split('\n')
        section_start = -1
        
        for i, line in enumerate(lines):
            line_lower = line.lower().strip()
            if any(keyword in line_lower for keyword in section_keywords):
                section_start = i
                break
        
        if section_start == -1:
            return ""
        
        # Extract section content (next 10-20 lines or until next section)
        section_lines = []
        for i in range(section_start + 1, min(section_start + 20, len(lines))):
            line = lines[i].strip()
            if not line:
                continue
            
            # Stop if we hit another section header
            if re.match(r'^[A-ZÄÖÜ][A-ZÄÖÜ\s]+$', line) and len(line) > 3:
                break
            
            section_lines.append(line)
        
        return '\n'.join(section_lines)
    
    def _looks_like_name(self, text: str) -> bool:
        """Check if text looks like a person's name"""
        if not text or len(text) < 2:
            return False
        
        # Should contain only letters, spaces, and common name characters
        if not re.match(r'^[A-ZÄÖÜa-zäöüß\s\-\.]+$', text):
            return False
        
        # Should not be too long
        if len(text) > 50:
            return False
        
        # Should not contain common non-name words
        non_name_words = ['telefon', 'email', 'straße', 'str', 'geboren', 'address']
        text_lower = text.lower()
        if any(word in text_lower for word in non_name_words):
            return False
        
        return True
