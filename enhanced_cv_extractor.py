"""
Enhanced CV Extractor for BAUCH HR Management System
Extracts comprehensive CV data including all requested fields:
- Personal information (name, first name, birthdate, address, nationality, marital status)
- Contact information (email, phone)
- Professional information (work history, qualifications, companies, job titles)
- Additional fields (language, notes, status)
"""

import re
import os
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple
import fitz  # PyMuPDF
from pathlib import Path

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

class EnhancedCVExtractor:
    """Enhanced CV extractor for comprehensive data extraction"""
    
    def __init__(self):
        self.german_months = {
            'januar': '01', 'februar': '02', 'märz': '03', 'april': '04',
            'mai': '05', 'juni': '06', 'juli': '07', 'august': '08',
            'september': '09', 'oktober': '10', 'november': '11', 'dezember': '12',
            'jan': '01', 'feb': '02', 'mär': '03', 'apr': '04',
            'jun': '06', 'jul': '07', 'aug': '08', 'sep': '09',
            'okt': '10', 'nov': '11', 'dez': '12'
        }
        
    def extract_comprehensive_cv_data(self, file_path: str, candidate_name: str = None, cv_status: str = "received") -> Dict[str, str]:
        """Extract all comprehensive CV data fields"""
        text = self._extract_text_from_file(file_path)
        if not text:
            # Return structure with empty values if text extraction fails
            filename = os.path.basename(file_path)
            return self._create_empty_result_structure(filename, candidate_name, cv_status)

        filename = os.path.basename(file_path)
        result = {}

        # Extract all fields
        result['name'] = self._extract_full_name(text, filename, candidate_name)
        result['first_name'] = self._extract_first_name(result['name'])
        result['birthdate'] = self._extract_birthdate(text)
        result['street'] = self._extract_street_address(text)
        result['postal_code'] = self._extract_postal_code(text)
        result['place_of_residence'] = self._extract_place_of_residence(text)
        result['nationality'] = self._extract_nationality(text)
        result['marital_status'] = self._extract_marital_status(text)
        result['language'] = self._extract_languages(text)
        result['work_experience_qualifications'] = self._extract_work_experience_and_qualifications(text)
        result['jobs_done'] = self._extract_jobs_done(text)
        result['past_companies'] = self._extract_past_companies(text)
        result['last_job_names'] = self._extract_last_job_names(text)
        result['notice_period'] = self._extract_notice_period(text)
        result['current_company'] = self._extract_current_company(text)
        result['free_field_notes'] = ""  # Empty field for HR notes
        result['upload_from_cv'] = filename
        result['status_of_application'] = cv_status

        return result

    def _create_empty_result_structure(self, filename: str, candidate_name: str = None, cv_status: str = "received") -> Dict[str, str]:
        """Create empty result structure with all required fields"""
        return {
            'name': candidate_name or "Unknown",
            'first_name': "",
            'birthdate': "",
            'street': "",
            'postal_code': "",
            'place_of_residence': "",
            'nationality': "",
            'marital_status': "",
            'language': "",
            'work_experience_qualifications': "",
            'jobs_done': "",
            'past_companies': "",
            'last_job_names': "",
            'notice_period': "",
            'current_company': "",
            'free_field_notes': "",
            'upload_from_cv': filename,
            'status_of_application': cv_status
        }
    
    def _extract_text_from_file(self, file_path: str) -> str:
        """Extract text from PDF, DOCX, or TXT file"""
        file_ext = Path(file_path).suffix.lower()

        if file_ext == '.pdf':
            try:
                with fitz.open(file_path) as doc:
                    return "\f".join(page.get_text("text") for page in doc)
            except Exception as e:
                print(f"Error reading PDF: {e}")
                return ""

        elif file_ext == '.docx' and DOCX_AVAILABLE:
            try:
                doc = Document(file_path)
                text_parts = []
                for paragraph in doc.paragraphs:
                    if paragraph.text.strip():
                        text_parts.append(paragraph.text.strip())
                return "\n".join(text_parts)
            except Exception as e:
                print(f"Error reading DOCX: {e}")
                return ""

        elif file_ext == '.txt':
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            except Exception as e:
                print(f"Error reading TXT: {e}")
                return ""

        return ""
    
    def _extract_full_name(self, text: str, filename: str, candidate_name: str = None) -> str:
        """Extract full name from CV"""
        if candidate_name:
            return candidate_name
        
        # Try to extract from first few lines
        lines = text.split('\n')[:10]
        for line in lines:
            line = line.strip()
            if self._looks_like_name(line):
                return line
        
        # Fallback to filename
        if filename:
            name_from_file = re.sub(r'[._-]', ' ', os.path.splitext(filename)[0])
            return name_from_file.title()
        
        return "Unknown"
    
    def _extract_first_name(self, full_name: str) -> str:
        """Extract first name from full name"""
        if not full_name or full_name == "Unknown":
            return ""
        
        parts = full_name.split()
        return parts[0] if parts else ""
    
    def _extract_birthdate(self, text: str) -> str:
        """Extract birthdate from CV"""
        # German date patterns
        date_patterns = [
            r'geboren\s*(?:am\s*)?(\d{1,2})[.\-/](\d{1,2})[.\-/](\d{4})',
            r'geburtsdatum\s*:?\s*(\d{1,2})[.\-/](\d{1,2})[.\-/](\d{4})',
            r'birth\s*date\s*:?\s*(\d{1,2})[.\-/](\d{1,2})[.\-/](\d{4})',
            r'born\s*:?\s*(\d{1,2})[.\-/](\d{1,2})[.\-/](\d{4})',
            r'(\d{1,2})[.\-/](\d{1,2})[.\-/](\d{4})\s*geboren',
        ]
        
        text_lower = text.lower()
        for pattern in date_patterns:
            match = re.search(pattern, text_lower)
            if match:
                day, month, year = match.groups()
                return f"{day.zfill(2)}.{month.zfill(2)}.{year}"
        
        return ""
    
    def _extract_street_address(self, text: str) -> str:
        """Extract street address from CV"""
        # German address patterns
        address_patterns = [
            r'([A-ZÄÖÜ][a-zäöüß]+(?:straße|str\.?|gasse|weg|platz|allee))\s*(\d+[a-z]?)',
            r'(\d+[a-z]?)\s+([A-ZÄÖÜ][a-zäöüß]+(?:straße|str\.?|gasse|weg|platz|allee))',
            r'adresse\s*:?\s*([^\n]+(?:straße|str\.?|gasse|weg|platz|allee)[^\n]*)',
        ]
        
        for pattern in address_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                if len(match.groups()) == 2:
                    return f"{match.group(1)} {match.group(2)}"
                else:
                    return match.group(1).strip()
        
        return ""
    
    def _extract_postal_code(self, text: str) -> str:
        """Extract postal code from CV"""
        # German postal code patterns (5 digits)
        postal_patterns = [
            r'\b(\d{5})\s+[A-ZÄÖÜ][a-zäöüß]+',  # 12345 München
            r'plz\s*:?\s*(\d{5})',
            r'postleitzahl\s*:?\s*(\d{5})',
        ]
        
        for pattern in postal_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return ""
    
    def _extract_place_of_residence(self, text: str) -> str:
        """Extract place of residence from CV"""
        # Look for city names after postal codes
        postal_city_pattern = r'\d{5}\s+([A-ZÄÖÜ][a-zäöüß]+(?:\s+[A-ZÄÖÜ][a-zäöüß]+)?)'
        match = re.search(postal_city_pattern, text)
        if match:
            return match.group(1)
        
        # Look for common German city indicators
        city_patterns = [
            r'wohnort\s*:?\s*([A-ZÄÖÜ][a-zäöüß]+)',
            r'ort\s*:?\s*([A-ZÄÖÜ][a-zäöüß]+)',
            r'stadt\s*:?\s*([A-ZÄÖÜ][a-zäöüß]+)',
        ]
        
        for pattern in city_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return ""
    
    def _extract_nationality(self, text: str) -> str:
        """Extract nationality from CV"""
        nationality_patterns = [
            r'nationalität\s*:?\s*([a-zäöüß]+)',
            r'staatsangehörigkeit\s*:?\s*([a-zäöüß]+)',
            r'nationality\s*:?\s*([a-z]+)',
            r'citizenship\s*:?\s*([a-z]+)',
        ]
        
        text_lower = text.lower()
        for pattern in nationality_patterns:
            match = re.search(pattern, text_lower)
            if match:
                return match.group(1).capitalize()
        
        return ""
    
    def _extract_marital_status(self, text: str) -> str:
        """Extract marital status from CV"""
        marital_patterns = [
            r'familienstand\s*:?\s*(ledig|verheiratet|geschieden|verwitwet)',
            r'marital\s*status\s*:?\s*(single|married|divorced|widowed)',
        ]
        
        text_lower = text.lower()
        for pattern in marital_patterns:
            match = re.search(pattern, text_lower)
            if match:
                return match.group(1).capitalize()
        
        return ""
    
    def _extract_languages(self, text: str) -> str:
        """Extract languages from CV using improved pattern matching"""
        # Look for language sections first
        language_section = self._find_section(text, ['sprachen', 'languages', 'sprachkenntnisse'])

        # If no dedicated section found, search in the entire text
        search_text = language_section if language_section else text

        if search_text:
            # Extract language names and levels with improved patterns
            languages = []
            lang_patterns = [
                r'(deutsch|englisch|französisch|spanisch|italienisch|russisch|polnisch|türkisch)\s*[:\-]?\s*(muttersprache|fließend|gut|grundkenntnisse|verhandlungssicher|sehr gut|gute kenntnisse|grundlagen)',
                r'(german|english|french|spanish|italian|russian|polish|turkish)\s*[:\-]?\s*(native|fluent|good|basic|business|excellent|intermediate|beginner)',
            ]

            for pattern in lang_patterns:
                matches = re.findall(pattern, search_text.lower())
                for lang, level in matches:
                    languages.append(f"{lang.capitalize()}: {level.capitalize()}")

            # If no structured patterns found, try simpler extraction
            if not languages:
                # Look for language names followed by level indicators in parentheses or after colon
                simple_patterns = [
                    r'(deutsch|englisch|französisch|spanisch|italienisch)\s*[:\(]?\s*([^\n,;)]+)',
                    r'(german|english|french|spanish|italian)\s*[:\(]?\s*([^\n,;)]+)',
                ]

                for pattern in simple_patterns:
                    matches = re.findall(pattern, search_text.lower())
                    for lang, level in matches:
                        level_clean = level.strip()[:30]  # Limit level description
                        # Filter out invalid level descriptions
                        if (level_clean and not level_clean.isdigit() and
                            len(level_clean) > 1 and
                            not any(word in level_clean for word in ['engineer', 'developer', 'company'])):
                            languages.append(f"{lang.capitalize()}: {level_clean.capitalize()}")

            # If still no languages found, try to find just language names
            if not languages:
                lang_only_patterns = [
                    r'\b(deutsch|englisch|französisch|spanisch|italienisch|russisch)\b',
                    r'\b(german|english|french|spanish|italian|russian)\b',
                ]

                for pattern in lang_only_patterns:
                    matches = re.findall(pattern, search_text.lower())
                    for lang in matches:
                        if lang not in [l.lower().split(':')[0] for l in languages]:
                            languages.append(f"{lang.capitalize()}")

            return "; ".join(languages[:5]) if languages else (language_section[:200] if language_section else "")

        return ""
    
    def _extract_work_experience_and_qualifications(self, text: str) -> str:
        """Extract work experience and qualifications using proven methods"""
        text_lower = text.lower()
        result_parts = []

        # 1. Extract years of experience first
        years_exp = self._extract_years_of_experience(text)
        if years_exp:
            result_parts.append(years_exp)

        # 2. Extract job positions and companies
        positions = self._extract_job_positions(text)
        if positions:
            result_parts.append(positions)

        # 3. Extract qualifications section
        qualifications = self._find_section(text, ['qualifikationen', 'qualifications', 'fähigkeiten', 'kompetenzen'])
        if qualifications:
            # Clean and limit qualifications
            qual_clean = qualifications.replace('\n', ' ').strip()
            if qual_clean:
                result_parts.append(f"Qualifikationen: {qual_clean[:300]}")

        # 4. If nothing found, try broader experience section
        if not result_parts:
            experience_section = self._find_section(text, ['berufserfahrung', 'work experience', 'professional experience'])
            if experience_section:
                result_parts.append(experience_section[:400])

        return " | ".join(result_parts) if result_parts else ""

    def _extract_years_of_experience(self, text: str) -> str:
        """Extract explicit years of experience mentions"""
        experience_patterns = [
            r'(\d+)\+?\s*Jahre?\s*(?:Berufs)?(?:erfahrung|tätigkeit|praxis)',
            r'(\d+)-(\d+)\s*Jahre?\s*(?:Berufs)?(?:erfahrung|tätigkeit)',
            r'(?:Berufs)?(?:erfahrung|tätigkeit)\s*:?\s*(\d+)\+?\s*Jahre?',
            r'(\d+)\+?\s*Jahre?\s*(?:in|im|als|mit)\s*[\w\s]+',
            r'seit\s*(\d+)\s*Jahre?n?\s*(?:tätig|beschäftigt|aktiv)',
            r'über\s*(\d+)\s*Jahre?\s*(?:Erfahrung|tätig|aktiv)',
            r'mehr\s*als\s*(\d+)\s*Jahre?\s*(?:Erfahrung|tätig)',
        ]

        for pattern in experience_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                if isinstance(matches[0], tuple):
                    # Range pattern (e.g., "2-4 Jahre")
                    return f"{matches[0][0]}-{matches[0][1]} Jahre Berufserfahrung"
                else:
                    # Single number pattern
                    years = matches[0]
                    return f"{years} Jahre Berufserfahrung"
        return ""

    def _extract_job_positions(self, text: str) -> str:
        """Extract job positions and companies"""
        lines = text.split('\n')
        positions = []

        # Look for lines that contain job titles and companies
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Pattern: "YYYY-YYYY: Position bei Company"
            job_match = re.search(r'(\d{4})\s*[-–]\s*(?:(\d{4})|heute|present):\s*([^,\n]+?)(?:\s+bei\s+([^,\n]+))?', line, re.IGNORECASE)
            if job_match:
                start_year, end_year, position, company = job_match.groups()
                end_year = end_year or "heute"
                position_clean = position.strip()

                # Only include if position is meaningful (more than 1 character)
                if len(position_clean) > 1:
                    if company:
                        company_clean = company.strip()
                        positions.append(f"{start_year}-{end_year}: {position_clean} bei {company_clean}")
                    else:
                        positions.append(f"{start_year}-{end_year}: {position_clean}")
                continue

            # Pattern: "Position bei Company (YYYY-YYYY)"
            alt_job_match = re.search(r'([^,\n]+?)\s+bei\s+([^,\n]+?)\s*\((\d{4})\s*[-–]\s*(?:(\d{4})|heute)\)', line, re.IGNORECASE)
            if alt_job_match:
                position, company, start_year, end_year = alt_job_match.groups()
                end_year = end_year or "heute"
                position_clean = position.strip()
                company_clean = company.strip()

                # Only include if position is meaningful
                if len(position_clean) > 1:
                    positions.append(f"{start_year}-{end_year}: {position_clean} bei {company_clean}")

        return "; ".join(positions[:5]) if positions else ""
    
    def _extract_jobs_done(self, text: str) -> str:
        """Extract list of job titles/positions done"""
        jobs = set()  # Use set to avoid duplicates

        # Method 1: Extract from timeline patterns (YYYY-YYYY: Job Title)
        timeline_pattern = r'(\d{4})\s*[-–]\s*(?:(\d{4})|heute|present)\s*:?\s*([^\n,]+?)(?:\s+bei\s+[^\n,]+)?'
        timeline_matches = re.findall(timeline_pattern, text, re.IGNORECASE)

        for match in timeline_matches:
            if len(match) >= 3:
                job_title = match[2].strip()
                # Clean up the job title and remove company info
                job_title = re.sub(r'\s+bei\s+.*$', '', job_title, flags=re.IGNORECASE)
                job_title = re.sub(r'\s+', ' ', job_title)  # Normalize whitespace
                # Filter out candidate names and invalid entries
                if (job_title and len(job_title) > 2 and
                    not self._is_candidate_name(job_title) and
                    not job_title.isdigit()):
                    jobs.add(job_title)

        # Method 2: Look for common job title patterns
        job_title_patterns = [
            r'(CNC[- ]?(?:Fräser|Dreher|Programmierer|Bediener))',
            r'(Senior\s+Software\s+Entwickler(?:in)?)',
            r'(Junior\s+Software\s+Entwickler(?:in)?)',
            r'(Software\s+Entwickler(?:in)?)',
            r'(Zerspanungs(?:mechaniker|techniker))',
            r'(Maschinen(?:bediener|führer|einrichter))',
            r'(Werkzeug(?:mechaniker|macher))',
            r'(Industrie(?:mechaniker|kaufmann))',
            r'([A-ZÄÖÜ][a-zäöüß\s\-]+(?:manager|ingenieur|techniker|leiter|spezialist))',
        ]

        for pattern in job_title_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                job_title = match.strip() if isinstance(match, str) else match[0].strip()
                # Clean and validate job title
                job_title = re.sub(r'\s+', ' ', job_title)
                if (job_title and len(job_title) > 2 and
                    not job_title.isdigit() and
                    not self._is_candidate_name(job_title)):
                    jobs.add(job_title)

        # Convert to list, clean duplicates, and limit
        jobs_list = []
        for job in jobs:
            # Skip if it's too similar to an existing job
            if not any(self._jobs_are_similar(job, existing) for existing in jobs_list):
                jobs_list.append(job)

        return "; ".join(jobs_list[:5]) if jobs_list else ""  # Limit to 5 most relevant jobs

    def _is_candidate_name(self, text: str) -> bool:
        """Check if text looks like a candidate name"""
        # Simple heuristic: if it's 2-3 words and doesn't contain job-related terms
        words = text.split()
        if len(words) <= 3:
            job_terms = ['entwickler', 'fräser', 'dreher', 'manager', 'techniker', 'mechaniker', 'bediener']
            if not any(term in text.lower() for term in job_terms):
                return True
        return False

    def _jobs_are_similar(self, job1: str, job2: str) -> bool:
        """Check if two job titles are similar enough to be considered duplicates"""
        job1_words = set(job1.lower().split())
        job2_words = set(job2.lower().split())

        # If they share more than 50% of words, consider them similar
        if len(job1_words) > 0 and len(job2_words) > 0:
            intersection = job1_words.intersection(job2_words)
            union = job1_words.union(job2_words)
            similarity = len(intersection) / len(union)
            return similarity > 0.5

        return False
    
    def _extract_past_companies(self, text: str) -> str:
        """Extract past companies"""
        companies = []
        
        # Look for company patterns
        company_patterns = [
            r'bei\s+([A-ZÄÖÜ][a-zäöüß\s&]+(?:GmbH|AG|KG|e\.V\.|Inc\.|Ltd\.|Corp))',
            r'([A-ZÄÖÜ][a-zäöüß\s&]+(?:GmbH|AG|KG|e\.V\.|Inc\.|Ltd\.|Corp))',
        ]
        
        for pattern in company_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                company = match.strip()
                if len(company) > 3 and company not in companies:
                    companies.append(company)
        
        return "; ".join(companies[:5]) if companies else ""  # Limit to 5 companies
    
    def _extract_last_job_names(self, text: str) -> str:
        """Extract last job names/titles"""
        # Look for most recent job (usually at the top of experience section)
        lines = text.split('\n')
        for i, line in enumerate(lines):
            if re.search(r'berufserfahrung|work experience|professional experience', line.lower()):
                # Look in next few lines for job title
                for j in range(i+1, min(i+10, len(lines))):
                    job_line = lines[j].strip()
                    if job_line and len(job_line) > 5:
                        # Check if it looks like a job title
                        if re.search(r'(manager|entwickler|ingenieur|techniker|leiter|spezialist|analyst)', job_line.lower()):
                            return job_line
        
        return ""
    
    def _extract_notice_period(self, text: str) -> str:
        """Extract notice period information"""
        notice_patterns = [
            r'kündigungsfrist\s*:?\s*([^\n]+)',
            r'notice\s*period\s*:?\s*([^\n]+)',
            r'verfügbar\s*ab\s*:?\s*([^\n]+)',
            r'available\s*from\s*:?\s*([^\n]+)',
        ]
        
        text_lower = text.lower()
        for pattern in notice_patterns:
            match = re.search(pattern, text_lower)
            if match:
                return match.group(1).strip()
        
        return ""
    
    def _extract_current_company(self, text: str) -> str:
        """Extract current company"""
        # Look for "seit" or "current" indicators
        current_patterns = [
            r'seit\s+\d{4}\s*:?\s*[^\n]*bei\s+([A-ZÄÖÜ][a-zäöüß\s&]+(?:GmbH|AG|KG|e\.V\.|Inc\.|Ltd\.|Corp))',
            r'current\s*:?\s*[^\n]*at\s+([A-Z][a-zA-Z\s&]+(?:GmbH|AG|KG|e\.V\.|Inc\.|Ltd\.|Corp))',
        ]
        
        for pattern in current_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return ""
    
    def _find_section(self, text: str, section_keywords: List[str]) -> str:
        """Find a specific section in the CV text"""
        lines = text.split('\n')
        section_start = -1
        
        for i, line in enumerate(lines):
            line_lower = line.lower().strip()
            if any(keyword in line_lower for keyword in section_keywords):
                section_start = i
                break
        
        if section_start == -1:
            return ""
        
        # Extract section content (next 10-20 lines or until next section)
        section_lines = []
        for i in range(section_start + 1, min(section_start + 20, len(lines))):
            line = lines[i].strip()
            if not line:
                continue
            
            # Stop if we hit another section header
            if re.match(r'^[A-ZÄÖÜ][A-ZÄÖÜ\s]+$', line) and len(line) > 3:
                break
            
            section_lines.append(line)
        
        return '\n'.join(section_lines)
    
    def _looks_like_name(self, text: str) -> bool:
        """Check if text looks like a person's name"""
        if not text or len(text) < 2:
            return False
        
        # Should contain only letters, spaces, and common name characters
        if not re.match(r'^[A-ZÄÖÜa-zäöüß\s\-\.]+$', text):
            return False
        
        # Should not be too long
        if len(text) > 50:
            return False
        
        # Should not contain common non-name words
        non_name_words = ['telefon', 'email', 'straße', 'str', 'geboren', 'address']
        text_lower = text.lower()
        if any(word in text_lower for word in non_name_words):
            return False
        
        return True
