#!/usr/bin/env python3
"""
Test script for enhanced CV extraction functionality
Tests the new comprehensive field extraction including:
- Personal information (name, birthdate, address, nationality, etc.)
- Professional information (work history, companies, qualifications, etc.)
- Additional fields (language, notice period, status, etc.)
"""

import os
import sys
import tempfile
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_extractor():
    """Test the enhanced CV extractor with comprehensive fields"""
    print("🔍 Testing Enhanced CV Extractor...")
    
    try:
        from enhanced_cv_extractor import EnhancedCVExtractor
        
        extractor = EnhancedCVExtractor()
        
        # Create a sample CV text for testing
        sample_cv_text = """
        Max Mustermann
        
        Persönliche Daten:
        Geboren: 15.03.1985
        Adresse: Musterstraße 123, 80331 München
        Nationalität: Deutsch
        Familienstand: Verheiratet
        Telefon: +49 89 123456789
        E-Mail: <EMAIL>
        
        Sprachen:
        Deutsch: Muttersprache
        Englisch: Fließend
        Französisch: Grundkenntnisse
        
        Berufserfahrung:
        2020-heute: Senior Software Entwickler bei TechCorp GmbH
        2018-2020: Software Entwickler bei StartupXYZ
        2016-2018: Junior Entwickler bei WebSolutions AG
        
        Qualifikationen:
        - 8 Jahre Erfahrung in der Softwareentwicklung
        - Expertise in Python, Java, JavaScript
        - Projektmanagement und Teamführung
        - Agile Entwicklungsmethoden
        
        Kündigungsfrist: 3 Monate zum Monatsende
        """
        
        # Create a temporary file with the sample CV
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp_file:
            tmp_file.write(sample_cv_text)
            tmp_file_path = tmp_file.name
        
        try:
            # Test comprehensive extraction
            result = extractor.extract_comprehensive_cv_data(
                tmp_file_path,
                candidate_name="Max Mustermann",
                cv_status="received"
            )
            
            print("✅ Enhanced extraction completed!")
            print("\n📋 Extracted Fields:")
            
            # Test all the requested fields
            expected_fields = [
                'name', 'first_name', 'birthdate', 'street', 'postal_code',
                'place_of_residence', 'nationality', 'marital_status', 'language',
                'work_experience_qualifications', 'jobs_done', 'past_companies',
                'last_job_names', 'notice_period', 'current_company',
                'free_field_notes', 'upload_from_cv', 'status_of_application'
            ]
            
            for field in expected_fields:
                value = result.get(field, 'N/A')
                print(f"   {field}: {value}")
            
            # Validate some key extractions
            validations = [
                ('name', 'Max Mustermann'),
                ('first_name', 'Max'),
                ('birthdate', '15.03.1985'),
                ('postal_code', '80331'),
                ('place_of_residence', 'München'),
                ('nationality', 'Deutsch'),
                ('marital_status', 'Verheiratet'),
                ('notice_period', '3 Monate zum Monatsende'),
                ('status_of_application', 'received')
            ]
            
            print("\n🔍 Validation Results:")
            all_passed = True
            for field, expected in validations:
                actual = result.get(field, '')
                if expected.lower() in actual.lower():
                    print(f"   ✅ {field}: PASS")
                else:
                    print(f"   ❌ {field}: FAIL (expected: {expected}, got: {actual})")
                    all_passed = False
            
            if all_passed:
                print("\n🎉 All validations passed!")
            else:
                print("\n⚠️ Some validations failed, but extraction is working.")
            
            return True
            
        finally:
            # Cleanup
            try:
                os.unlink(tmp_file_path)
            except (OSError, PermissionError):
                pass
        
    except Exception as e:
        print(f"❌ Enhanced extractor test failed: {e}")
        return False

def test_excel_integration():
    """Test integration with Excel extraction route"""
    print("\n📊 Testing Excel Integration...")
    
    try:
        from app_german import app
        
        with app.test_client() as client:
            # Test the extract-excel route with comprehensive fields
            response = client.get('/extract-excel?job=Test Job&count=all&fields=comprehensive')
            
            # Should redirect if no job found, but route should exist
            print(f"✅ Excel extraction route accessible (status: {response.status_code})")
            
            return True
            
    except Exception as e:
        print(f"❌ Excel integration test failed: {e}")
        return False

def test_field_mapping():
    """Test that all requested fields are properly mapped"""
    print("\n🗂️ Testing Field Mapping...")
    
    try:
        from enhanced_cv_extractor import EnhancedCVExtractor
        
        extractor = EnhancedCVExtractor()
        
        # Test with empty file to check field structure
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp_file:
            tmp_file.write("Empty CV for structure test")
            tmp_file_path = tmp_file.name
        
        try:
            result = extractor.extract_comprehensive_cv_data(tmp_file_path)
            
            # Check that all requested fields are present
            required_fields = [
                'name', 'first_name', 'birthdate', 'street', 'postal_code',
                'place_of_residence', 'nationality', 'marital_status', 'language',
                'work_experience_qualifications', 'jobs_done', 'past_companies',
                'last_job_names', 'notice_period', 'current_company',
                'free_field_notes', 'upload_from_cv', 'status_of_application'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field not in result:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ Missing fields: {missing_fields}")
                return False
            else:
                print(f"✅ All {len(required_fields)} required fields present")
                return True
                
        finally:
            try:
                os.unlink(tmp_file_path)
            except (OSError, PermissionError):
                pass
        
    except Exception as e:
        print(f"❌ Field mapping test failed: {e}")
        return False

def main():
    """Run all enhanced extraction tests"""
    print("🚀 Starting Enhanced CV Extraction Tests\n")
    
    tests = [
        test_enhanced_extractor,
        test_excel_integration,
        test_field_mapping
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All enhanced extraction tests passed!")
        print("\n📋 Summary of Enhanced Fields:")
        print("   ✅ Personal Information: Name, First Name, Birthdate, Address, Nationality, Marital Status")
        print("   ✅ Contact Information: Email, Phone, Languages")
        print("   ✅ Professional Information: Work Experience, Qualifications, Jobs Done, Companies")
        print("   ✅ Employment Details: Last Job Names, Notice Period, Current Company")
        print("   ✅ Administrative Fields: Notes, Upload Source, Application Status")
        return True
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
